#!/usr/bin/env python3
"""
Test script for AI Content Generation functionality
Tests the complete flow from tool creation to content generation
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api"
TIMEOUT = 30

def print_status(message, status="INFO"):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {status}: {message}")

def test_api_health():
    """Test if the API is responding"""
    try:
        # Try the tools endpoint as a health check
        response = requests.get(f"{BASE_URL}/tools?limit=1", timeout=5)
        if response.status_code == 200:
            print_status("✅ API is healthy", "SUCCESS")
            return True
        else:
            print_status(f"❌ API health check failed: {response.status_code}", "ERROR")
            return False
    except Exception as e:
        print_status(f"❌ API health check failed: {e}", "ERROR")
        return False

def create_test_tool():
    """Create a test tool for content generation"""
    tool_data = {
        "name": "Test Content Generation Tool",
        "category": "Text Generation",
        "short_description": "A test tool to verify content generation functionality",
        "company": "Test Company",
        "website": "https://test-example.com"
    }
    
    try:
        print_status("Creating test tool...")
        response = requests.post(f"{BASE_URL}/tools", json=tool_data, timeout=TIMEOUT)
        
        if response.status_code == 200:
            tool = response.json()
            print_status(f"✅ Tool created successfully: {tool['id']}", "SUCCESS")
            print_status(f"   Name: {tool['name']}")
            print_status(f"   Status: {tool.get('ai_generation_status', 'unknown')}")
            return tool
        else:
            print_status(f"❌ Tool creation failed: {response.status_code}", "ERROR")
            print_status(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print_status(f"❌ Tool creation failed: {e}", "ERROR")
        return None

def generate_content(tool_id):
    """Generate AI content for a tool"""
    try:
        print_status(f"Starting content generation for tool: {tool_id}")
        
        # Record start time
        start_time = time.time()
        
        response = requests.post(f"{BASE_URL}/tools/{tool_id}/generate-content", timeout=TIMEOUT)
        
        # Record end time
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            print_status(f"✅ Content generation completed in {duration:.2f}s", "SUCCESS")
            print_status(f"   Success: {result.get('success', False)}")
            print_status(f"   Quality Score: {result.get('quality_score', 'N/A')}")
            print_status(f"   Message: {result.get('message', 'No message')}")
            return result
        else:
            print_status(f"❌ Content generation failed: {response.status_code}", "ERROR")
            print_status(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print_status(f"❌ Content generation failed: {e}", "ERROR")
        return None

def verify_generated_content(tool_id):
    """Verify that content was properly generated"""
    try:
        print_status(f"Verifying generated content for tool: {tool_id}")
        
        response = requests.get(f"{BASE_URL}/tools", timeout=TIMEOUT)
        
        if response.status_code == 200:
            data = response.json()
            tools = data.get('items', [])
            
            # Find our tool
            tool = next((t for t in tools if t['id'] == tool_id), None)
            
            if tool:
                print_status("✅ Tool found in tools list", "SUCCESS")
                print_status(f"   AI Generation Status: {tool.get('ai_generation_status', 'unknown')}")
                print_status(f"   Generation Status: {tool.get('generation_status', 'unknown')}")
                print_status(f"   Quality Score: {tool.get('quality_score', 'N/A')}")
                print_status(f"   Has Description: {bool(tool.get('description'))}")
                print_status(f"   Has Features: {bool(tool.get('features'))}")
                print_status(f"   Has Haiku: {bool(tool.get('haiku'))}")
                
                # Check if status is completed
                if tool.get('ai_generation_status') == 'completed':
                    print_status("✅ Content generation status is 'completed'", "SUCCESS")
                    return True
                else:
                    print_status(f"❌ Expected status 'completed', got '{tool.get('ai_generation_status')}'", "ERROR")
                    return False
            else:
                print_status(f"❌ Tool {tool_id} not found in tools list", "ERROR")
                return False
        else:
            print_status(f"❌ Failed to fetch tools: {response.status_code}", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"❌ Content verification failed: {e}", "ERROR")
        return False

def test_stats_update():
    """Test that stats are updated after content generation"""
    try:
        print_status("Checking system stats...")
        
        response = requests.get(f"{BASE_URL}/stats", timeout=TIMEOUT)
        
        if response.status_code == 200:
            stats = response.json()
            print_status("✅ Stats retrieved successfully", "SUCCESS")
            print_status(f"   Total Tools: {stats.get('total_tools', 0)}")
            print_status(f"   Completed Tools: {stats.get('completed_tools', 0)}")
            print_status(f"   Average Quality Score: {stats.get('average_quality_score', 0):.1f}")
            return True
        else:
            print_status(f"❌ Failed to fetch stats: {response.status_code}", "ERROR")
            return False
            
    except Exception as e:
        print_status(f"❌ Stats check failed: {e}", "ERROR")
        return False

def main():
    """Run the complete content generation test"""
    print_status("🚀 Starting AI Content Generation Test", "INFO")
    print_status("=" * 50)
    
    # Test 1: API Health
    if not test_api_health():
        print_status("❌ Test failed: API is not healthy", "ERROR")
        sys.exit(1)
    
    # Test 2: Create Tool
    tool = create_test_tool()
    if not tool:
        print_status("❌ Test failed: Could not create test tool", "ERROR")
        sys.exit(1)
    
    tool_id = tool['id']
    
    # Test 3: Generate Content
    result = generate_content(tool_id)
    if not result:
        print_status("❌ Test failed: Content generation failed", "ERROR")
        sys.exit(1)
    
    # Test 4: Verify Content
    if not verify_generated_content(tool_id):
        print_status("❌ Test failed: Content verification failed", "ERROR")
        sys.exit(1)
    
    # Test 5: Check Stats
    if not test_stats_update():
        print_status("❌ Test failed: Stats check failed", "ERROR")
        sys.exit(1)
    
    print_status("=" * 50)
    print_status("🎉 All tests passed! Content generation is working correctly", "SUCCESS")
    print_status(f"   Test tool ID: {tool_id}")
    print_status("   You can now test the frontend by clicking the 'Generate AI Content' button")

if __name__ == "__main__":
    main()
