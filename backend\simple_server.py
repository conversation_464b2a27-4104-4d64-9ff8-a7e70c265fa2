#!/usr/bin/env python3
"""
Simplified DudeAI AI Agent System Backend Server
For quick local development testing
"""
import os
import sys
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import json

from fastapi import FastAPI, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Simple configuration
ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000"
]

# Create FastAPI app
app = FastAPI(
    title="DudeAI AI Agent System",
    description="A comprehensive AI agent system for managing AI tools",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add trusted host middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.localhost"]
)

# Simple data models
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    database: Dict[str, Any]
    version: str

class StatsResponse(BaseModel):
    total_tools: int
    completed_tools: int
    published_tools: int
    verified_tools: int
    pending_tools: int
    processing_tools: int
    failed_tools: int
    average_quality_score: float
    automation_rate: float
    publish_rate: float

class CategoryResponse(BaseModel):
    categories: List[str]

class SimpleTool(BaseModel):
    id: str
    name: str
    website: Optional[str] = None
    category: str
    short_description: Optional[str] = None
    company: Optional[str] = None
    logo_url: Optional[str] = None
    screenshots: Optional[List[str]] = []
    is_verified: bool = False
    content_status: str = "draft"
    ai_generation_status: Optional[str] = "pending"
    generation_status: Optional[str] = "pending"
    content_quality_score: Optional[float] = None
    quality_score: Optional[float] = None
    description: Optional[str] = None
    ai_description: Optional[str] = None
    features: Optional[List[str]] = []
    ai_features: Optional[List[str]] = []
    pros_and_cons: Optional[Dict[str, List[str]]] = None
    ai_pros: Optional[List[str]] = []
    ai_cons: Optional[List[str]] = []
    haiku: Optional[str] = None
    created_at: datetime
    updated_at: datetime

class ToolCreate(BaseModel):
    name: str = Field(..., min_length=2, max_length=100)
    website: Optional[str] = None
    category: str
    short_description: Optional[str] = None
    company: Optional[str] = None
    logo_url: Optional[str] = None
    screenshots: Optional[List[str]] = []

class ToolUpdate(BaseModel):
    name: Optional[str] = None
    website: Optional[str] = None
    category: Optional[str] = None
    short_description: Optional[str] = None
    company: Optional[str] = None
    logo_url: Optional[str] = None
    screenshots: Optional[List[str]] = None

class PaginatedResponse(BaseModel):
    items: List[SimpleTool]
    pagination: Dict[str, Any]

class GenerationResponse(BaseModel):
    success: bool
    message: str
    tool_id: str
    quality_score: Optional[float] = None

# Mock data storage (in production, this would be a database)
mock_tools = []

# Predefined categories
CATEGORIES = [
    "Text Generation",
    "Image Generation", 
    "Video Generation",
    "Audio Generation",
    "Code Generation",
    "Data Analysis",
    "Chatbots",
    "Automation",
    "Design",
    "Marketing",
    "Productivity",
    "Education",
    "Research",
    "Content Creation",
    "Translation",
    "Summarization",
    "Other"
]

# API Routes
@app.get("/api/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow().isoformat(),
        database={
            "status": "healthy",
            "response_time_ms": 15.2
        },
        version="1.0.0"
    )

@app.get("/api/stats", response_model=StatsResponse)
async def get_stats():
    """Get system statistics"""
    total = len(mock_tools)
    completed = len([t for t in mock_tools if t.get('ai_generation_status') == 'completed'])
    processing = len([t for t in mock_tools if t.get('ai_generation_status') == 'processing'])
    failed = len([t for t in mock_tools if t.get('ai_generation_status') == 'failed'])
    pending = len([t for t in mock_tools if t.get('ai_generation_status') == 'pending'])

    # Calculate average quality score
    quality_scores = [t.get('quality_score', 0) for t in mock_tools if t.get('quality_score') is not None]
    avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

    return StatsResponse(
        total_tools=total,
        completed_tools=completed,
        published_tools=completed,
        verified_tools=len([t for t in mock_tools if t.get('is_verified', False)]),
        pending_tools=pending,
        processing_tools=processing,
        failed_tools=failed,
        average_quality_score=avg_quality,
        automation_rate=(completed / total * 100) if total > 0 else 0.0,
        publish_rate=(completed / total * 100) if total > 0 else 0.0
    )

@app.get("/api/categories", response_model=CategoryResponse)
async def get_categories():
    """Get all available categories"""
    return CategoryResponse(categories=CATEGORIES)

@app.get("/api/tools", response_model=PaginatedResponse)
async def get_tools(
    page: int = 1,
    page_size: int = 20,
    category: Optional[str] = None,
    status: Optional[str] = None,
    verified: Optional[bool] = None,
    search: Optional[str] = None
):
    """Get paginated list of AI tools with filtering"""
    try:
        # Filter tools
        filtered_tools = mock_tools.copy()
        
        if category:
            filtered_tools = [t for t in filtered_tools if t.get('category') == category]
        
        if status:
            filtered_tools = [t for t in filtered_tools if t.get('content_status') == status]
            
        if verified is not None:
            filtered_tools = [t for t in filtered_tools if t.get('is_verified') == verified]
            
        if search:
            search_lower = search.lower()
            filtered_tools = [
                t for t in filtered_tools 
                if search_lower in t.get('name', '').lower() or 
                   search_lower in t.get('short_description', '').lower()
            ]
        
        # Pagination
        total_count = len(filtered_tools)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        paginated_tools = filtered_tools[start_idx:end_idx]
        
        # Convert to SimpleTool objects
        tools = []
        for tool_data in paginated_tools:
            tools.append(SimpleTool(**tool_data))
        
        return PaginatedResponse(
            items=tools,
            pagination={
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting tools: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tools"
        )

@app.post("/api/tools", response_model=SimpleTool)
async def create_tool(tool_data: ToolCreate):
    """Create a new AI tool"""
    try:
        import uuid
        
        # Create new tool
        new_tool = {
            "id": str(uuid.uuid4()),
            "name": tool_data.name,
            "website": tool_data.website,
            "category": tool_data.category,
            "short_description": tool_data.short_description,
            "company": tool_data.company,
            "logo_url": tool_data.logo_url,
            "screenshots": tool_data.screenshots or [],
            "is_verified": False,
            "content_status": "draft",
            "ai_generation_status": "pending",
            "generation_status": "pending",
            "content_quality_score": None,
            "quality_score": None,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        mock_tools.append(new_tool)
        
        return SimpleTool(**new_tool)
        
    except Exception as e:
        logger.error(f"Error creating tool: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create tool"
        )

@app.get("/api/tools/{tool_id}", response_model=SimpleTool)
async def get_tool(tool_id: str):
    """Get a specific tool by ID"""
    try:
        tool = next((t for t in mock_tools if t['id'] == tool_id), None)
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tool not found"
            )
        
        return SimpleTool(**tool)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tool {tool_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tool"
        )

@app.post("/api/tools/{tool_id}/generate-content", response_model=GenerationResponse)
async def generate_content(tool_id: str):
    """Generate AI content for a tool"""
    try:
        tool = next((t for t in mock_tools if t['id'] == tool_id), None)
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tool not found"
            )

        # Set status to processing
        tool['ai_generation_status'] = 'processing'
        tool['generation_status'] = 'processing'
        tool['updated_at'] = datetime.utcnow()

        # Simulate AI content generation processing time
        await asyncio.sleep(2)  # Simulate processing time

        # Generate comprehensive AI content based on tool info
        tool_name = tool.get('name', 'Unknown Tool')
        tool_category = tool.get('category', 'General')
        tool_description = tool.get('short_description', '')

        # Generate detailed description
        generated_description = f"{tool_name} is a powerful AI tool in the {tool_category} category. {tool_description} This tool offers advanced capabilities and features designed to enhance productivity and streamline workflows for users across various industries."

        # Generate features list
        generated_features = [
            f"Advanced {tool_category.lower()} capabilities",
            "User-friendly interface",
            "High-quality output generation",
            "Customizable settings and parameters",
            "Integration with popular platforms",
            "Real-time processing and results"
        ]

        # Generate pros and cons
        generated_pros = [
            "Excellent performance and reliability",
            "Intuitive and easy to use",
            "High-quality results",
            "Regular updates and improvements",
            "Good customer support"
        ]

        generated_cons = [
            "May require learning curve for beginners",
            "Pricing could be expensive for some users",
            "Limited customization in free version"
        ]

        # Calculate quality score
        quality_score = 85.5 + (len(tool_name) * 0.5) + (len(generated_features) * 2.0)
        quality_score = min(quality_score, 100.0)  # Cap at 100

        # Update tool with generated content
        tool.update({
            'content_status': 'published',
            'ai_generation_status': 'completed',
            'generation_status': 'completed',
            'description': generated_description,
            'ai_description': generated_description,
            'features': generated_features,
            'ai_features': generated_features,
            'pros_and_cons': {
                'pros': generated_pros,
                'cons': generated_cons
            },
            'ai_pros': generated_pros,
            'ai_cons': generated_cons,
            'content_quality_score': quality_score,
            'quality_score': quality_score,
            'haiku': f"{tool_name} shines bright,\nAI power at your fingertips,\nFuture made simple.",
            'updated_at': datetime.utcnow()
        })

        return GenerationResponse(
            success=True,
            message="Content generated successfully",
            tool_id=tool_id,
            quality_score=quality_score
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating content for tool {tool_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate content"
        )

# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "Internal server error"}
    )

# Startup event
@app.on_event("startup")
async def startup_event():
    """Application startup"""
    logger.info("DudeAI AI Agent System starting...")
    
    # Add some sample data
    sample_tools = [
        {
            "id": "1",
            "name": "ChatGPT",
            "website": "https://chat.openai.com",
            "category": "Text Generation",
            "short_description": "AI chatbot by OpenAI",
            "company": "OpenAI",
            "logo_url": None,
            "screenshots": [],
            "is_verified": True,
            "content_status": "published",
            "ai_generation_status": "completed",
            "generation_status": "completed",
            "content_quality_score": 92.5,
            "quality_score": 92.5,
            "description": "ChatGPT is an advanced AI chatbot developed by OpenAI that uses natural language processing to engage in human-like conversations. It can assist with a wide range of tasks including writing, coding, analysis, and creative projects.",
            "ai_description": "ChatGPT is an advanced AI chatbot developed by OpenAI that uses natural language processing to engage in human-like conversations. It can assist with a wide range of tasks including writing, coding, analysis, and creative projects.",
            "features": ["Natural language conversations", "Code generation and debugging", "Creative writing assistance", "Question answering", "Language translation"],
            "ai_features": ["Natural language conversations", "Code generation and debugging", "Creative writing assistance", "Question answering", "Language translation"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "id": "2",
            "name": "DALL-E",
            "website": "https://openai.com/dall-e-2",
            "category": "Image Generation",
            "short_description": "AI image generator by OpenAI",
            "company": "OpenAI",
            "logo_url": None,
            "screenshots": [],
            "is_verified": True,
            "content_status": "draft",
            "ai_generation_status": "pending",
            "generation_status": "pending",
            "content_quality_score": None,
            "quality_score": None,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    ]
    
    mock_tools.extend(sample_tools)
    logger.info("Sample data loaded")
    logger.info("Application startup completed successfully")

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
