#!/usr/bin/env python3
"""
Complete system integration test for DudeAI AI Agent System
Tests both frontend and backend integration
"""
import requests
import time
import json
from datetime import datetime

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_success(msg):
    print(f"{Colors.GREEN}✓ {msg}{Colors.END}")

def print_error(msg):
    print(f"{Colors.RED}✗ {msg}{Colors.END}")

def print_warning(msg):
    print(f"{Colors.YELLOW}⚠ {msg}{Colors.END}")

def print_info(msg):
    print(f"{Colors.BLUE}ℹ {msg}{Colors.END}")

def print_header(msg):
    print(f"\n{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.BLUE} {msg}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")

def test_backend_health():
    """Test backend health and basic functionality"""
    print_header("Testing Backend Health & API")
    
    try:
        # Test health endpoint
        response = requests.get('http://localhost:8000/api/health', timeout=5)
        if response.status_code == 200:
            print_success("Backend health check passed")
            data = response.json()
            print_info(f"Status: {data['status']}, Version: {data['version']}")
        else:
            print_error(f"Backend health check failed: {response.status_code}")
            return False
            
        # Test API documentation
        response = requests.get('http://localhost:8000/docs', timeout=5)
        if response.status_code == 200:
            print_success("API documentation accessible")
        else:
            print_warning("API documentation not accessible")
            
        return True
        
    except Exception as e:
        print_error(f"Backend test failed: {e}")
        return False

def test_frontend_accessibility():
    """Test frontend accessibility"""
    print_header("Testing Frontend Accessibility")
    
    try:
        # Test frontend homepage
        response = requests.get('http://localhost:3000', timeout=10)
        if response.status_code == 200:
            print_success("Frontend homepage accessible")
            
            # Check if it contains expected content
            content = response.text
            if 'DudeAI' in content or 'AI Agent System' in content:
                print_success("Frontend contains expected content")
            else:
                print_warning("Frontend content may not be fully loaded")
                
            return True
        else:
            print_error(f"Frontend not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Frontend test failed: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration between frontend and backend"""
    print_header("Testing CORS Configuration")
    
    try:
        # Simulate frontend request with CORS headers
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        # Test preflight request
        response = requests.options('http://localhost:8000/api/health', headers=headers, timeout=5)
        
        if response.status_code in [200, 204]:
            print_success("CORS preflight request successful")
            
            # Check CORS headers
            cors_headers = response.headers
            if 'Access-Control-Allow-Origin' in cors_headers:
                origin = cors_headers['Access-Control-Allow-Origin']
                if origin in ['http://localhost:3000', '*']:
                    print_success(f"CORS origin correctly configured: {origin}")
                else:
                    print_warning(f"CORS origin may be incorrect: {origin}")
            else:
                print_error("CORS headers missing")
                return False
                
            return True
        else:
            print_error(f"CORS preflight failed: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"CORS test failed: {e}")
        return False

def test_api_functionality():
    """Test key API functionality"""
    print_header("Testing API Functionality")
    
    try:
        base_url = 'http://localhost:8000/api'
        
        # Test getting tools
        response = requests.get(f'{base_url}/tools', timeout=5)
        if response.status_code == 200:
            print_success("Tools listing works")
            data = response.json()
            print_info(f"Found {len(data['items'])} tools")
        else:
            print_error("Tools listing failed")
            return False
            
        # Test getting categories
        response = requests.get(f'{base_url}/categories', timeout=5)
        if response.status_code == 200:
            print_success("Categories endpoint works")
            data = response.json()
            print_info(f"Found {len(data['categories'])} categories")
        else:
            print_error("Categories endpoint failed")
            return False
            
        # Test creating a tool
        new_tool = {
            "name": f"Integration Test Tool {int(time.time())}",
            "website": "https://example.com",
            "category": "Text Generation",
            "short_description": "A tool created during integration testing",
            "company": "Test Company"
        }
        
        response = requests.post(f'{base_url}/tools', json=new_tool, timeout=10)
        if response.status_code == 200:
            print_success("Tool creation works")
            tool_data = response.json()
            tool_id = tool_data['id']
            print_info(f"Created tool with ID: {tool_id}")
            
            # Test content generation
            response = requests.post(f'{base_url}/tools/{tool_id}/generate-content', timeout=15)
            if response.status_code == 200:
                print_success("Content generation works")
                gen_data = response.json()
                print_info(f"Quality score: {gen_data.get('quality_score', 'N/A')}")
            else:
                print_warning("Content generation failed")
                
        else:
            print_error("Tool creation failed")
            return False
            
        return True
        
    except Exception as e:
        print_error(f"API functionality test failed: {e}")
        return False

def test_system_performance():
    """Test basic system performance"""
    print_header("Testing System Performance")
    
    try:
        # Test response times
        start_time = time.time()
        response = requests.get('http://localhost:8000/api/health', timeout=5)
        backend_time = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            if backend_time < 1000:  # Less than 1 second
                print_success(f"Backend response time: {backend_time:.2f}ms")
            else:
                print_warning(f"Backend response time slow: {backend_time:.2f}ms")
        
        # Test frontend response time
        start_time = time.time()
        response = requests.get('http://localhost:3000', timeout=10)
        frontend_time = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            if frontend_time < 3000:  # Less than 3 seconds
                print_success(f"Frontend response time: {frontend_time:.2f}ms")
            else:
                print_warning(f"Frontend response time slow: {frontend_time:.2f}ms")
                
        return True
        
    except Exception as e:
        print_error(f"Performance test failed: {e}")
        return False

def generate_system_report():
    """Generate a comprehensive system status report"""
    print_header("System Status Report")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "backend": {
            "status": "unknown",
            "url": "http://localhost:8000",
            "api_docs": "http://localhost:8000/docs"
        },
        "frontend": {
            "status": "unknown", 
            "url": "http://localhost:3000"
        },
        "integration": {
            "cors": "unknown",
            "api_communication": "unknown"
        }
    }
    
    # Check backend
    try:
        response = requests.get('http://localhost:8000/api/health', timeout=5)
        report["backend"]["status"] = "healthy" if response.status_code == 200 else "error"
    except:
        report["backend"]["status"] = "offline"
    
    # Check frontend
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        report["frontend"]["status"] = "healthy" if response.status_code == 200 else "error"
    except:
        report["frontend"]["status"] = "offline"
    
    # Check CORS
    try:
        headers = {'Origin': 'http://localhost:3000'}
        response = requests.options('http://localhost:8000/api/health', headers=headers, timeout=5)
        report["integration"]["cors"] = "working" if response.status_code in [200, 204] else "error"
    except:
        report["integration"]["cors"] = "error"
    
    # Print report
    print_info("Backend Status: " + report["backend"]["status"])
    print_info("Frontend Status: " + report["frontend"]["status"])
    print_info("CORS Status: " + report["integration"]["cors"])
    
    # Save report
    with open('system_status_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print_info("Detailed report saved to: system_status_report.json")
    
    return report

def main():
    """Main test function"""
    print(f"{Colors.BOLD}{Colors.BLUE}")
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                DudeAI System Integration Test                ║")
    print("║                  Full Stack Verification                    ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print(f"{Colors.END}")
    
    print_info("Testing complete DudeAI AI Agent System integration...")
    print_info("This will verify frontend-backend communication and functionality")
    
    # Run all tests
    tests = [
        ("Backend Health", test_backend_health),
        ("Frontend Accessibility", test_frontend_accessibility), 
        ("CORS Configuration", test_cors_configuration),
        ("API Functionality", test_api_functionality),
        ("System Performance", test_system_performance)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print_info(f"Running {test_name} test...")
        results[test_name] = test_func()
        time.sleep(1)  # Brief pause between tests
    
    # Generate final report
    system_report = generate_system_report()
    
    # Summary
    print_header("Test Results Summary")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        if result:
            print_success(f"{test_name}: PASSED")
        else:
            print_error(f"{test_name}: FAILED")
    
    print(f"\n{Colors.BOLD}Overall Result: {passed}/{total} tests passed{Colors.END}")
    
    if passed == total:
        print_success("🎉 All tests passed! System is fully functional!")
        print_info("✅ Frontend: http://localhost:3000")
        print_info("✅ Backend API: http://localhost:8000/api")
        print_info("✅ API Docs: http://localhost:8000/docs")
        print_info("✅ CORS: Properly configured")
        print_info("✅ Integration: Working correctly")
    else:
        print_error("❌ Some tests failed. Check the output above for details.")
    
    print(f"\n{Colors.BOLD}System is ready for development! 🚀{Colors.END}")

if __name__ == "__main__":
    main()
