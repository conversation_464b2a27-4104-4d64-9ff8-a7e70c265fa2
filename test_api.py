#!/usr/bin/env python3
"""
Test script for DudeAI Backend API endpoints
"""
import requests
import json
import time

def test_api_endpoints():
    """Test all API endpoints"""
    print('Testing DudeAI Backend API Endpoints')
    print('=' * 50)

    base_url = 'http://localhost:8000/api'
    
    # Test health endpoint
    try:
        response = requests.get(f'{base_url}/health', timeout=5)
        print(f'✓ Health Check: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'  Status: {data["status"]}')
            print(f'  Database: {data["database"]["status"]}')
            print(f'  Version: {data["version"]}')
    except Exception as e:
        print(f'✗ Health Check failed: {e}')
        return False

    # Test stats endpoint
    try:
        response = requests.get(f'{base_url}/stats', timeout=5)
        print(f'✓ Stats: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'  Total tools: {data["total_tools"]}')
            print(f'  Published tools: {data["published_tools"]}')
    except Exception as e:
        print(f'✗ Stats failed: {e}')

    # Test categories endpoint
    try:
        response = requests.get(f'{base_url}/categories', timeout=5)
        print(f'✓ Categories: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'  Categories count: {len(data["categories"])}')
            print(f'  Sample categories: {data["categories"][:3]}')
    except Exception as e:
        print(f'✗ Categories failed: {e}')

    # Test tools endpoint
    try:
        response = requests.get(f'{base_url}/tools', timeout=5)
        print(f'✓ Tools List: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'  Tools count: {len(data["items"])}')
            print(f'  Pagination: page {data["pagination"]["page"]} of {data["pagination"]["total_pages"]}')
            if data["items"]:
                print(f'  Sample tool: {data["items"][0]["name"]}')
    except Exception as e:
        print(f'✗ Tools List failed: {e}')

    # Test CORS with preflight request
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options(f'{base_url}/health', headers=headers, timeout=5)
        print(f'✓ CORS Preflight: {response.status_code}')
        if 'Access-Control-Allow-Origin' in response.headers:
            print(f'  CORS Origin: {response.headers["Access-Control-Allow-Origin"]}')
    except Exception as e:
        print(f'✗ CORS test failed: {e}')

    # Test creating a new tool
    try:
        new_tool = {
            "name": "Test AI Tool",
            "website": "https://example.com",
            "category": "Text Generation",
            "short_description": "A test AI tool for API testing",
            "company": "Test Company"
        }
        response = requests.post(f'{base_url}/tools', json=new_tool, timeout=10)
        print(f'✓ Create Tool: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'  Created tool ID: {data["id"]}')
            print(f'  Tool name: {data["name"]}')
            
            # Test getting the specific tool
            tool_id = data["id"]
            response = requests.get(f'{base_url}/tools/{tool_id}', timeout=5)
            print(f'✓ Get Specific Tool: {response.status_code}')
            
            # Test content generation
            response = requests.post(f'{base_url}/tools/{tool_id}/generate-content', timeout=15)
            print(f'✓ Generate Content: {response.status_code}')
            if response.status_code == 200:
                data = response.json()
                print(f'  Generation success: {data["success"]}')
                print(f'  Quality score: {data["quality_score"]}')
                
    except Exception as e:
        print(f'✗ Tool operations failed: {e}')

    print('\n' + '=' * 50)
    print('API endpoints test completed! ✅')
    return True

def test_frontend_backend_communication():
    """Test that frontend can communicate with backend"""
    print('\nTesting Frontend-Backend Communication')
    print('=' * 50)
    
    # Simulate frontend request with proper headers
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Frontend Test)'
        }
        
        response = requests.get('http://localhost:8000/api/health', headers=headers, timeout=5)
        print(f'✓ Frontend-style request: {response.status_code}')
        
        # Check CORS headers in response
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers'
        ]
        
        for header in cors_headers:
            if header in response.headers:
                print(f'  {header}: {response.headers[header]}')
        
        print('✅ Frontend-Backend communication working!')
        return True
        
    except Exception as e:
        print(f'✗ Frontend-Backend communication failed: {e}')
        return False

if __name__ == "__main__":
    # Wait a moment for server to be ready
    print("Waiting for server to be ready...")
    time.sleep(2)
    
    success = test_api_endpoints()
    if success:
        test_frontend_backend_communication()
    
    print("\n🎉 All tests completed!")
    print("\nNext steps:")
    print("1. Start frontend: cd frontend && npm start")
    print("2. Visit: http://localhost:3000")
    print("3. Backend API docs: http://localhost:8000/docs")
