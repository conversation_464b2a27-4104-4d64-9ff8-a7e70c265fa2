#!/usr/bin/env python3
"""
Simple CORS test for DudeAI system
"""
import requests

def test_cors():
    print("Testing CORS Configuration...")
    
    # Test preflight request
    headers = {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
    }
    
    try:
        response = requests.options('http://localhost:8000/api/health', headers=headers, timeout=5)
        print(f"Preflight Status: {response.status_code}")
        
        cors_headers = {}
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                cors_headers[header] = value
                print(f"  {header}: {value}")
        
        # Test actual request
        response = requests.get('http://localhost:8000/api/health', 
                              headers={'Origin': 'http://localhost:3000'}, timeout=5)
        print(f"Actual Request Status: {response.status_code}")
        
        if 'Access-Control-Allow-Origin' in response.headers:
            origin = response.headers['Access-Control-Allow-Origin']
            print(f"Response CORS Header: {origin}")
            if origin in ['http://localhost:3000', '*']:
                print("✅ CORS is working correctly!")
                return True
            else:
                print("❌ CORS origin mismatch")
                return False
        else:
            print("❌ CORS headers missing in response")
            return False
            
    except Exception as e:
        print(f"CORS test error: {e}")
        return False

if __name__ == "__main__":
    test_cors()
